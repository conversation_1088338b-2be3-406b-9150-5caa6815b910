#Requires AutoHotkey v2.0
#SingleInstance Force
; Simple Screenshot Capture Tool
; Captures screenshots and saves them to the "snips" folder

; --- Global hotkey variable ---
global selectedHotkey := ""

; --- Configure hotkey at startup ---
ConfigureHotkey()

; --- Hotkey configuration function ---
ConfigureHotkey() {
    global selectedHotkey

    ; Create hotkey selection GUI
    hotkeyGui := Gui("+AlwaysOnTop", "Choose Hotkey - SnipAI Simple Capture")
    hotkeyGui.SetFont("s12", "Segoe UI")
    hotkeyGui.MarginX := 20
    hotkeyGui.MarginY := 15

    hotkeyGui.Add("Text", "w300", "Choose your preferred hotkey for screenshot capture:")
    hotkeyGui.Add("Text", "w300 y+10", "")

    ; Radio buttons for hotkey selection
    scrollRadio := hotkeyGui.Add("Radio", "w300 y+5 Checked vScrollRadio", "ScrollLock (recommended)")
    f12Radio := hotkeyGui.Add("Radio", "w300 y+5 vF12Radio", "F12")

    hotkeyGui.Add("Text", "w300 y+15", "")
    okBtn := hotkeyGui.Add("Button", "w100 h30", "OK")

    ; Event handler using a simpler approach
    okBtn.OnEvent("Click", (*) => OnHotkeyOkClick(hotkeyGui))

    hotkeyGui.Show()
}

; --- Handle OK button click ---
OnHotkeyOkClick(gui) {
    global selectedHotkey

    ; Get radio button values directly from the GUI
    if (gui["ScrollRadio"].Value) {
        selectedHotkey := "ScrollLock"
    } else {
        selectedHotkey := "F12"
    }

    gui.Destroy()
    SetupHotkeyAndShowInstructions()
}

; --- Setup the chosen hotkey and show instructions ---
SetupHotkeyAndShowInstructions() {
    global selectedHotkey

    ; Register the chosen hotkey
    if (selectedHotkey = "ScrollLock") {
        Hotkey "ScrollLock", (*) => CaptureAndSaveScreenshot()
    } else if (selectedHotkey = "F12") {
        Hotkey "F12", (*) => CaptureAndSaveScreenshot()
    }

    ; Show startup instructions with the chosen hotkey
    MsgBox "Simple Screenshot Capture Tool`n`n" .
           "• Press " selectedHotkey " to capture a screenshot`n" .
           "• Screenshots are automatically saved to the 'snips' folder`n" .
           "• Files are named snip_001.png, snip_002.png, etc.`n" .
           "• No GUI - works silently in the background`n`n" .
           "The tool is now ready to use!", "SnipAI - Simple Capture", 64
}

; --- Main capture and save function ---
CaptureAndSaveScreenshot() {
    ; Ensure snips directory exists
    snipsDir := A_ScriptDir "\snips"
    if (!DirExist(snipsDir)) {
        try {
            DirCreate snipsDir
        } catch Error as e {
            return  ; Silently fail if can't create directory
        }
    }

    ; Use Windows built-in screen snipping tool
    static ScreenSnipperProcessName := "ScreenClippingHost.exe"
    SavedClip := ClipboardAll()
    A_Clipboard := "" ; clear for detection
    RunWait "ms-screenclip:"
    WinWaitActive "ahk_exe " ScreenSnipperProcessName,, 2

    ; Wait until user finishes snip
    Loop {
        DllCall("user32.dll\GetCursorPos", "int64P", &pt64:=0)
        try {
            if WinGetProcessName(hWnd := DllCall("GetAncestor", "Ptr"
                , DllCall("user32.dll\WindowFromPoint", "int64", pt64, "ptr")
                , "UInt", 2, "ptr")) != ScreenSnipperProcessName
                break
        } catch
            break
    }

    ClipWait(1, 1)
    Sleep 100

    ; Check if clipboard has bitmap data
    if !DllCall("IsClipboardFormatAvailable", "uint", 2) {
        A_Clipboard := SavedClip
        return
    }

    ; Extract HBITMAP from clipboard
    DllCall("OpenClipboard", "ptr", A_ScriptHwnd)
    hData := DllCall("GetClipboardData", "uint", 2, "ptr")
    if (!hData) {
        DllCall("CloseClipboard")
        A_Clipboard := SavedClip
        return
    }

    hBitmap := DllCall("User32.dll\CopyImage", "UPtr", hData
        , "UInt", 0, "Int", 0, "Int", 0, "UInt", 0x2000, "Ptr")
    DllCall("CloseClipboard")

    if (!hBitmap) {
        A_Clipboard := SavedClip
        return
    }

    ; Get the current next available number in real-time
    currentNumber := GetNextSnipNumber()
    filename := "snip_" Format("{:03d}", currentNumber) ".png"
    outFile := snipsDir "\" filename

    ; Save the bitmap as PNG with retry mechanism
    if (SaveBitmapWithRetry(hBitmap, outFile)) {
        ; Show success tooltip
        ShowSuccessTooltip("Screenshot saved: " filename)
    } else {
        ; Show error message if all retries failed
        MsgBox "Failed to save screenshot after multiple attempts.`n`nFile: " filename "`nLocation: " snipsDir, "Save Error", 48
    }

    ; Restore clipboard
    A_Clipboard := SavedClip
}

; --- Save HBITMAP as PNG ---
SaveBitmapToPNG(hBitmap, outFile) {
    try {
        ; Load GDI+ library
        hMod := DllCall("GetModuleHandle", "str", "gdiplus", "ptr")
        if !hMod
            hMod := DllCall("LoadLibrary", "str", "gdiplus", "ptr")
        if !hMod
            return false

        ; Initialize GDI+
        si := Buffer(16, 0)
        NumPut("UInt", 1, si)
        result := DllCall("gdiplus\GdiplusStartup", "ptr*", &pToken := 0, "ptr", si, "ptr", 0)
        if (result != 0)
            return false

        ; Create bitmap from HBITMAP
        result := DllCall("gdiplus\GdipCreateBitmapFromHBITMAP", "ptr", hBitmap, "ptr", 0, "ptr*", &pBitmap := 0)
        if (result != 0) {
            DllCall("gdiplus\GdiplusShutdown", "ptr", pToken)
            return false
        }

        ; PNG CLSID
        CLSID := Buffer(16, 0)
        DllCall("ole32\CLSIDFromString", "wstr", "{557CF406-1A04-11D3-9A73-0000F81EF32E}", "ptr", CLSID)

        ; Save image to file
        result := DllCall("gdiplus\GdipSaveImageToFile", "ptr", pBitmap, "wstr", outFile, "ptr", CLSID, "ptr", 0)

        ; Cleanup
        DllCall("gdiplus\GdipDisposeImage", "ptr", pBitmap)
        DllCall("gdiplus\GdiplusShutdown", "ptr", pToken)

        return (result = 0)
    } catch {
        return false
    }
}

; --- Save bitmap with retry logic ---
SaveBitmapWithRetry(hBitmap, outFile) {
    maxRetries := 5
    baseDelay := 500  ; Start with 500ms delay

    Loop maxRetries {
        try {
            ; Attempt to save the bitmap
            if (SaveBitmapToPNG(hBitmap, outFile)) {
                ; Validate the saved file
                if (IsValidSavedFile(outFile)) {
                    return true  ; Success
                }
            }
        } catch Error as e {
            ; If it's a critical error, don't continue retrying
            if InStr(StrLower(e.Message), "access denied") || InStr(StrLower(e.Message), "sharing violation") {
                return false
            }
        }

        ; If not the last attempt, wait before retrying
        if (A_Index < maxRetries) {
            ; Progressive delay: 500ms, 1000ms, 1500ms, 2000ms
            delay := baseDelay * A_Index
            Sleep delay
        }
    }

    return false  ; All retries failed
}

; --- Validate that file was saved correctly ---
IsValidSavedFile(filePath) {
    ; Check if file exists
    if (!FileExist(filePath))
        return false

    ; Check file size (should be > 1KB for a valid PNG)
    try {
        size := FileGetSize(filePath)
        if (size < 1024)
            return false
    } catch {
        return false
    }

    ; Try to read the file to ensure it's not locked/corrupted
    try {
        file := FileOpen(filePath, "r")
        if (!file)
            return false

        ; Read first few bytes to check PNG signature
        headerBuf := Buffer(8)
        file.RawRead(headerBuf)
        file.Close()

        ; PNG files start with specific signature: 89 50 4E 47 0D 0A 1A 0A
        ; Check at least the first 4 bytes match PNG signature
        return (NumGet(headerBuf, 0, "UChar") = 0x89 &&
                NumGet(headerBuf, 1, "UChar") = 0x50 &&
                NumGet(headerBuf, 2, "UChar") = 0x4E &&
                NumGet(headerBuf, 3, "UChar") = 0x47)

    } catch {
        return false
    }
}

; --- Get next snip number by checking existing files ---
GetNextSnipNumber() {
    snipsDir := A_ScriptDir "\snips"

    ; If snips directory doesn't exist, start from 1
    if (!DirExist(snipsDir)) {
        return 1
    }

    maxNumber := 0

    ; Check all existing snip files to find the highest number
    try {
        Loop Files, snipsDir "\snip_*.png" {
            ; Extract number from filename using regex
            if RegExMatch(A_LoopFileName, "snip_(\d+)\.png", &match) {
                number := Integer(match[1])
                if (number > maxNumber) {
                    maxNumber := number
                }
            }
        }
    } catch {
        ; If there's an error reading the directory, start from 1
        return 1
    }

    ; Return next number (maxNumber + 1)
    return maxNumber + 1
}

; --- Global tooltip GUI variable ---
global tooltipGui := ""

; --- Show success tooltip that auto-disappears ---
ShowSuccessTooltip(message) {
    global tooltipGui

    ; Destroy previous tooltip if it exists
    if (tooltipGui != "") {
        try tooltipGui.Destroy()
        tooltipGui := ""
    }

    ; Get primary monitor dimensions for centering
    MonitorGet(1, &Left, &Top, &Right, &Bottom)
    screenCenterX := Left + (Right - Left) // 2
    screenCenterY := Top + (Bottom - Top) // 2

    ; Create styled tooltip GUI
    tooltipGui := Gui("+AlwaysOnTop -Caption +ToolWindow", "")
    tooltipGui.BackColor := "0x2D2D30"  ; Dark background
    tooltipGui.MarginX := 25
    tooltipGui.MarginY := 20

    ; Add text with large, readable font
    tooltipGui.SetFont("s16 Bold", "Segoe UI")
    textCtrl := tooltipGui.Add("Text", "c0xFFFFFF Center w300", message)  ; White text, centered, fixed width

    ; Show tooltip hidden first to get dimensions
    tooltipGui.Show("Hide")
    tooltipGui.GetPos(,, &guiWidth, &guiHeight)

    ; Calculate center position
    centerX := screenCenterX - (guiWidth // 2)
    centerY := screenCenterY - (guiHeight // 2)

    ; Show tooltip at center of screen
    tooltipGui.Show("x" centerX " y" centerY " NoActivate")

    ; Set timer to hide tooltip after 2.5 seconds
    SetTimer(() => HideTooltip(), -2500)
}

; --- Hide tooltip function ---
HideTooltip() {
    global tooltipGui
    if (tooltipGui != "") {
        try {
            tooltipGui.Destroy()
            tooltipGui := ""
        }
    }
}


