[general]
; Show startup message when application starts
show_startup_message=false

; Hotkey for taking screenshots (ScrollLock by default)
hotkey=ScrollLock

[image]
; Image encoding method: base64 or upload
; base64: Encode images directly as base64 (faster, no internet required for upload)
; upload: Upload to 0x0.st service (requires internet, may be more reliable for large images)
encoding_method=base64

; Maximum upload retries for 0x0.st service
upload_max_retries=5

; Base delay between upload retries in milliseconds
upload_base_delay=1000

; Delete local image file after successful upload and API response
delete_after_upload=true

; Image file naming pattern (uses A_Now for timestamp)
filename_pattern=Snip_{timestamp}.png

[api]
; OpenRouter API configuration
url=https://openrouter.ai/api/v1/chat/completions
key=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
model=x-ai/grok-4

; Maximum API retries for failed requests
max_retries=5

; Base delay between API retries in milliseconds
base_delay=2000

; API request timeout in milliseconds
timeout=60000

[prompt]
; Default prompt sent to the AI model
default=You are an expert assistant. Analyze the image and identify any questions, problems, or instructions. Do exactly what is mentioned or asked in the image. Restate the main task or question clearly in plain text, then provide only the direct answer or result in plain text on a new line. Use simple formats like '3/4' for fractions and 'x^2' for exponents (no LaTeX markup like frac{} or ^). Don't add explanations or extra details. Structure it as: Task/Question: [restated task]`n`nResult/Answer: [answer]`n`nResult/choice: [choice].

[ui]
; Show tooltips during processing
show_tooltips=true

; Tooltip display duration in milliseconds
tooltip_duration=3000

; Show progress during uploads and API calls
show_progress=true

[paths]
; Path to curl executable (relative to script directory)
curl_path=bin\curl\curl.exe

; Temporary directory for upload results (uses A_Temp by default)
temp_dir=

; Output directory for screenshots (uses script directory by default)
output_dir=
