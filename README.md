# SnipAI - Screenshot OCR and AI Analysis Tool

SnipAI is an AutoHotkey v2 application that captures screenshots, processes them with AI, and provides intelligent analysis of the content.

## Features

- **Screenshot Capture**: Press ScrollLock to capture a screenshot using Windows' built-in screen snipping tool
- **Flexible Image Handling**: Choose between direct base64 encoding or uploading to 0x0.st
- **AI Analysis**: Send images to OpenRouter API for intelligent analysis
- **Configurable Settings**: All settings are managed through a `config.ini` file
- **No Startup Dialogs**: Configuration-driven behavior eliminates annoying startup prompts

## Quick Start

1. Run `snip_ai.ahk` with AutoHotkey v2
2. The application will create a default `config.ini` file on first run
3. Press ScrollLock to capture a screenshot and get AI analysis
4. Customize settings by editing `config.ini`

## Configuration

All settings are stored in `config.ini`. The file is automatically created with default values on first run.

### [general] Section

- `show_startup_message` (true/false): Show informational message when application starts
- `hotkey` (string): Hotkey for taking screenshots (default: ScrollLock)

### [image] Section

- `encoding_method` (base64/upload): How to handle images
  - `base64`: Encode images directly as base64 (faster, no internet required for upload)
  - `upload`: Upload to 0x0.st service (requires internet, may be more reliable for large images)
- `upload_max_retries` (number): Maximum upload retries for 0x0.st service (default: 5)
- `upload_base_delay` (milliseconds): Base delay between upload retries (default: 1000)
- `delete_after_upload` (true/false): Delete local image file after successful upload and API response
- `filename_pattern` (string): Image file naming pattern (default: Snip_{timestamp}.png)

### [api] Section

- `url` (string): OpenRouter API endpoint
- `key` (string): Your OpenRouter API key
- `model` (string): AI model to use (e.g., "x-ai/grok-4", "qwen/qwen2.5-vl-32b-instruct:free")
- `max_retries` (number): Maximum API retries for failed requests (default: 3)
- `base_delay` (milliseconds): Base delay between API retries (default: 3000)
- `timeout` (milliseconds): API request timeout (default: 60000)

### [prompt] Section

- `default` (string): Default prompt sent to the AI model

### [ui] Section

- `show_tooltips` (true/false): Show tooltips during processing
- `tooltip_duration` (milliseconds): Tooltip display duration (default: 3000)
- `show_progress` (true/false): Show progress during uploads and API calls

### [paths] Section

- `curl_path` (string): Path to curl executable (default: bin\curl\curl.exe)
- `temp_dir` (string): Temporary directory for upload results (uses system temp if empty)
- `output_dir` (string): Output directory for screenshots (uses script directory if empty)

## Example Configuration

```ini
[general]
show_startup_message=true
hotkey=ScrollLock

[image]
encoding_method=base64
upload_max_retries=5
upload_base_delay=1000
delete_after_upload=true
filename_pattern=Snip_{timestamp}.png

[api]
url=https://openrouter.ai/api/v1/chat/completions
key=your-api-key-here
model=x-ai/grok-4
max_retries=3
base_delay=3000
timeout=60000

[ui]
show_tooltips=true
tooltip_duration=3000
show_progress=true
```

## Requirements

- AutoHotkey v2.0
- Windows 10/11 (for built-in screen snipping tool)
- curl.exe (included in bin\curl\ directory)
- OpenRouter API key
- Internet connection (for API calls and optional image uploads)

## Error Handling

The application includes robust error handling:

- **Missing config.ini**: Creates default configuration file automatically
- **Invalid config values**: Falls back to sensible defaults
- **Upload failures**: Automatically falls back to base64 encoding
- **API failures**: Implements exponential backoff retry logic
- **Network timeouts**: Configurable timeout values

## Troubleshooting

1. **No response from AI**: Check your API key and internet connection
2. **Upload failures**: Try switching to base64 encoding method
3. **Screenshot not captured**: Ensure Windows screen snipping tool is available
4. **Configuration not loading**: Check config.ini file permissions and syntax

## License

This project is provided as-is for educational and personal use.
