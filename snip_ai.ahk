#Requires AutoHotkey v2.0
#SingleInstance Force
; SnipAI - Screenshot OCR and AI Analysis Tool
; Configuration is loaded from config.ini

; --- Auto-restart on first launch for clean GDI+ state ---
;if (!A_Args.Length || A_Args[1] != "restarted") {
;    Run A_AhkPath ' "' A_ScriptFullPath '" restarted'
;    ExitApp
;}

; --- Load Configuration ---
global config := LoadConfiguration()
global previewGui

; Initial instructions (if enabled in config)
if (config.general.show_startup_message) {
    MsgBox "Press ScrollLock to snip a screenshot, analyze with AI, and display the response.`n`nConfiguration loaded from config.ini"
}

; --- Auto-enter capture mode on startup ---
SetTimer AutoStartCapture, -1000  ; 1 second delay to ensure full initialization

AutoStartCapture() {
    ; Trigger capture workflow immediately after startup
    CaptureWorkflow()
}

; --- Configuration Loading Function ---
LoadConfiguration() {
    configFile := A_ScriptDir "\config.ini"
    config := {}

    ; Default configuration values
    config.general := {
        show_startup_message: true,
        hotkey: "ScrollLock"
    }

    config.image := {
        encoding_method: "base64",
        upload_max_retries: 5,
        upload_base_delay: 1000,
        delete_after_upload: true,
        filename_pattern: "Snip_{timestamp}.png"
    }

    config.api := {
        url: "https://openrouter.ai/api/v1/chat/completions",
        key: "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",
        model: "x-ai/grok-4",
        max_retries: 3,
        base_delay: 3000,
        timeout: 60000
    }

    config.prompt := {
        default: "You are an expert assistant. Analyze the image and identify any questions, problems, or instructions. Do exactly what is mentioned or asked in the image. Restate the main task or question clearly in plain text, then provide only the direct answer or result in plain text on a new line. Use simple formats like '3/4' for fractions and 'x^2' for exponents (no LaTeX markup like frac{} or ^). Don't add explanations or extra details. Structure it as: Task/Question: [restated task]`n`nResult/Answer: [answer]`n`nResult/choice: [choice]."
    }

    config.ui := {
        show_tooltips: true,
        tooltip_duration: 3000,
        show_progress: true
    }

    config.paths := {
        curl_path: "bin\curl\curl.exe",
        temp_dir: "",
        output_dir: ""
    }

    ; Load configuration from file if it exists
    if FileExist(configFile) {
        try {
            ; Read general section
            config.general.show_startup_message := IniRead(configFile, "general", "show_startup_message", "true") = "true"
            config.general.hotkey := IniRead(configFile, "general", "hotkey", "ScrollLock")

            ; Read image section
            config.image.encoding_method := IniRead(configFile, "image", "encoding_method", "base64")
            config.image.upload_max_retries := Integer(IniRead(configFile, "image", "upload_max_retries", "5"))
            config.image.upload_base_delay := Integer(IniRead(configFile, "image", "upload_base_delay", "1000"))
            config.image.delete_after_upload := IniRead(configFile, "image", "delete_after_upload", "true") = "true"
            config.image.filename_pattern := IniRead(configFile, "image", "filename_pattern", "Snip_{timestamp}.png")

            ; Read API section
            config.api.url := IniRead(configFile, "api", "url", "https://openrouter.ai/api/v1/chat/completions")
            config.api.key := IniRead(configFile, "api", "key", "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")
            config.api.model := IniRead(configFile, "api", "model", "x-ai/grok-4")
            config.api.max_retries := Integer(IniRead(configFile, "api", "max_retries", "3"))
            config.api.base_delay := Integer(IniRead(configFile, "api", "base_delay", "3000"))
            config.api.timeout := Integer(IniRead(configFile, "api", "timeout", "60000"))

            ; Read prompt section
            config.prompt.default := IniRead(configFile, "prompt", "default", config.prompt.default)

            ; Read UI section
            config.ui.show_tooltips := IniRead(configFile, "ui", "show_tooltips", "true") = "true"
            config.ui.tooltip_duration := Integer(IniRead(configFile, "ui", "tooltip_duration", "3000"))
            config.ui.show_progress := IniRead(configFile, "ui", "show_progress", "true") = "true"

            ; Read paths section
            config.paths.curl_path := IniRead(configFile, "paths", "curl_path", "bin\curl\curl.exe")
            config.paths.temp_dir := IniRead(configFile, "paths", "temp_dir", "")
            config.paths.output_dir := IniRead(configFile, "paths", "output_dir", "")

        } catch Error as e {
            MsgBox "Warning: Error reading config.ini: " e.Message "`nUsing default values.", "Configuration Warning", 48
        }
    } else {
        ; Create default config file
        try {
            CreateDefaultConfigFile(configFile)
            MsgBox "Created default config.ini file. You can edit it to customize settings.", "Configuration", 64
        } catch Error as e {
            MsgBox "Warning: Could not create config.ini: " e.Message "`nUsing default values.", "Configuration Warning", 48
        }
    }

    ; Validate and set derived values
    if (config.paths.temp_dir = "") {
        config.paths.temp_dir := A_Temp
    }
    if (config.paths.output_dir = "") {
        config.paths.output_dir := A_ScriptDir
    }

    return config
}

; --- Create Default Configuration File ---
CreateDefaultConfigFile(configFile) {
    content := "[general]`n"
    content .= "; Show startup message when application starts`n"
    content .= "show_startup_message=true`n`n"
    content .= "; Hotkey for taking screenshots (ScrollLock by default)`n"
    content .= "hotkey=ScrollLock`n`n"

    content .= "[image]`n"
    content .= "; Image encoding method: base64 or upload`n"
    content .= "; base64: Encode images directly as base64 (faster, no internet required for upload)`n"
    content .= "; upload: Upload to 0x0.st service (requires internet, may be more reliable for large images)`n"
    content .= "encoding_method=base64`n`n"
    content .= "; Maximum upload retries for 0x0.st service`n"
    content .= "upload_max_retries=5`n`n"
    content .= "; Base delay between upload retries in milliseconds`n"
    content .= "upload_base_delay=1000`n`n"
    content .= "; Delete local image file after successful upload and API response`n"
    content .= "delete_after_upload=true`n`n"
    content .= "; Image file naming pattern (uses A_Now for timestamp)`n"
    content .= "filename_pattern=Snip_{timestamp}.png`n`n"

    content .= "[api]`n"
    content .= "; OpenRouter API configuration`n"
    content .= "url=https://openrouter.ai/api/v1/chat/completions`n"
    content .= "key=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`n"
    content .= "model=x-ai/grok-4`n`n"
    content .= "; Maximum API retries for failed requests`n"
    content .= "max_retries=3`n`n"
    content .= "; Base delay between API retries in milliseconds`n"
    content .= "base_delay=3000`n`n"
    content .= "; API request timeout in milliseconds`n"
    content .= "timeout=60000`n`n"

    content .= "[prompt]`n"
    content .= "; Default prompt sent to the AI model`n"
    content .= "default=You are an expert assistant. Analyze the image and identify any questions, problems, or instructions. Do exactly what is mentioned or asked in the image. Restate the main task or question clearly in plain text, then provide only the direct answer or result in plain text on a new line. Use simple formats like '3/4' for fractions and 'x^2' for exponents (no LaTeX markup like frac{} or ^). Don't add explanations or extra details. Structure it as: Task/Question: [restated task]`n`nResult/Answer: [answer]`n`nResult/choice: [choice].`n`n"

    content .= "[ui]`n"
    content .= "; Show tooltips during processing`n"
    content .= "show_tooltips=true`n`n"
    content .= "; Tooltip display duration in milliseconds`n"
    content .= "tooltip_duration=3000`n`n"
    content .= "; Show progress during uploads and API calls`n"
    content .= "show_progress=true`n`n"

    content .= "[paths]`n"
    content .= "; Path to curl executable (relative to script directory)`n"
    content .= "curl_path=bin\curl\curl.exe`n`n"
    content .= "; Temporary directory for upload results (uses A_Temp by default)`n"
    content .= "temp_dir=`n`n"
    content .= "; Output directory for screenshots (uses script directory by default)`n"
    content .= "output_dir=`n"

    FileAppend content, configFile
}

; --- Image Encoding Functions ---
; Determine if we should use base64 encoding based on configuration
UseBase64Encoding() {
    return config.image.encoding_method = "base64"
}

; --- Hotkey: ScrollLock to restart app for fresh capture ---
ScrollLock::{
    ; Clean up current captured file and preview GUI before restart
    global previewGui
    try {
        if IsSet(previewGui) && previewGui.DeletePath {
            TryDeleteFile(previewGui.DeletePath)
        }
        if IsSet(previewGui) {
            try previewGui.Destroy()
        }
    }

    ; Also clean up any existing Snip files in output directory
    try {
        Loop Files, config.paths.output_dir "\Snip_*.png" {
            TryDeleteFile(A_LoopFileFullPath)
        }
    }

    ; Clean restart for fresh GDI+ state
    Reload
}

; --- Main capture workflow function ---
CaptureWorkflow() {
    ; Verify file system health before starting
    if (!VerifyFileSystemHealth(config.paths.output_dir)) {
        ; Clean up any existing files before restart
        try {
            Loop Files, config.paths.output_dir "\Snip_*.png" {
                TryDeleteFile(A_LoopFileFullPath)
            }
        }
        MsgBox "File system health check failed. Restarting application.", "System Error", 48
        Reload
        return
    }

    static ScreenSnipperProcessName := "ScreenClippingHost.exe"
    SavedClip := ClipboardAll()
    A_Clipboard := "" ; clear for detection
    RunWait "ms-screenclip:"
    WinWaitActive "ahk_exe " ScreenSnipperProcessName,, 2

    ; Wait until user finishes snip
    Loop {
        DllCall("user32.dll\GetCursorPos", "int64P", &pt64:=0)
        try {
            if WinGetProcessName(hWnd := DllCall("GetAncestor", "Ptr"
                , DllCall("user32.dll\WindowFromPoint", "int64", pt64, "ptr")
                , "UInt", 2, "ptr")) != ScreenSnipperProcessName
                break
        } catch
            break
    }

    ClipWait(1, 1)
    Sleep 100

    if !DllCall("IsClipboardFormatAvailable", "uint", 2) {
        A_Clipboard := SavedClip
        return
    }

    ; Extract HBITMAP from clipboard
    DllCall("OpenClipboard", "ptr", A_ScriptHwnd)
    hData := DllCall("GetClipboardData", "uint", 2, "ptr")
    if (!hData) {
        DllCall("CloseClipboard")
        ShowSimplePreview("", "", "")  ; Show empty preview
        UpdatePreviewStatus("Error: No bitmap data found in clipboard. Restarting...")
        Sleep 3000  ; Give user time to read the error
        Reload
        return
    }

    hBitmap := DllCall("User32.dll\CopyImage", "UPtr", hData
        , "UInt", 0, "Int", 0, "Int", 0, "UInt", 0x2000, "Ptr")
    DllCall("CloseClipboard")

    if (!hBitmap) {
        ShowSimplePreview("", "", "")  ; Show empty preview
        UpdatePreviewStatus("Error: Failed to copy bitmap from clipboard. Restarting...")
        Sleep 3000  ; Give user time to read the error
        Reload
        return
    }

    ; Save bitmap with configured filename pattern and retry logic
    filename := StrReplace(config.image.filename_pattern, "{timestamp}", A_Now)
    outFile := config.paths.output_dir "\" filename

    ; Retry file saving with automatic restart fallback
    if (!SaveBitmapWithRetry(hBitmap, outFile)) {
        ; If all retries failed, show error in preview and restart
        A_Clipboard := SavedClip
        ShowSimplePreview("", "", "")  ; Show empty preview
        UpdatePreviewStatus("Critical Error: File save failed after multiple attempts. Restarting application...")
        Sleep 3000  ; Give user time to read the error
        TryDeleteFile(outFile)  ; Delete any corrupted/incomplete file
        Reload
        return
    }

    A_Clipboard := SavedClip

    ; Final validation check before proceeding
    UpdatePreviewStatus("Validating saved file...")
    if (!IsValidSavedFile(outFile)) {
        UpdatePreviewStatus("Critical Error: File validation failed. Restarting application...")
        Sleep 3000  ; Give user time to read the error
        TryDeleteFile(outFile)  ; Delete invalid file before restart
        Reload
        return
    }

    ; Show preview immediately with progress indicator
    ShowSimplePreview(outFile, "", "")
    PreviewStartProgress("Starting analysis...")
    UpdatePreviewStatus("Preparing image for analysis...")

    ; Determine imageSource based on configuration
    useBase64 := UseBase64Encoding()

    if useBase64 {
        UpdatePreviewStatus("Using direct base64 encoding...")
        imageSource := outFile  ; Use local file for base64
    } else {
        ; Try upload to 0x0.st
        UpdatePreviewStatus("Uploading image...")
        uploadedUrl := UploadTo0x0(outFile)
        if (uploadedUrl = "") {
            UpdatePreviewStatus("Upload failed. Using direct base64...")
            imageSource := outFile  ; Fallback to local file for base64
        } else {
            UpdatePreviewStatus("Upload successful. Processing...")
            imageSource := uploadedUrl  ; Use uploaded URL
        }
    }

    ; Verify file still exists and is valid before API call
    UpdatePreviewStatus("Re-validating file before API call...")
    if (!IsValidSavedFile(outFile)) {
        UpdatePreviewStatus("Critical Error: Image file became invalid during processing. Restarting...")
        Sleep 3000  ; Give user time to read the error
        TryDeleteFile(outFile)  ; Delete corrupted file before restart
        Reload
        return
    }

    ; Get response from OpenRouter using configured values
    answer := GetImageDescription(config.api.url, config.api.key, config.api.model, imageSource, config.prompt.default)

    ; Check if response contains an error
    if InStr(answer, "Error:") {
        ; Display error in progress text control instead of answer box
        UpdatePreviewStatus("API Error: " answer)
        PreviewSetAnswer("")  ; Clear answer box for errors
        ; Keep progress bar visible to show error status
    } else {
        ; Post-process for better readability (fallback)
        answer := PostProcessMath(answer)

        ; Simplify final answer to format: (X) Text
        simple := ExtractSimpleAnswer(answer)

        ; Update preview with final answer and stop progress
        PreviewSetAnswer(simple)
        PreviewStopProgress()
        UpdatePreviewStatus("Analysis complete")
    }

    ; Set deletion on close if success and configured
    if (!InStr(answer, "Error:") && config.image.delete_after_upload) {
        try {
            if IsSet(previewGui)
                previewGui.DeletePath := outFile
        }
    }
}

; --- Save HBITMAP as PNG with error checking ---
SaveBitmapToPNG(hBitmap, outFile) {
    ; Validate input parameters
    if (!hBitmap)
        throw Error("Invalid HBITMAP handle")
    if (!outFile)
        throw Error("Invalid output file path")

    ; Load GDI+ library
    hMod := DllCall("GetModuleHandle", "str", "gdiplus", "ptr")
    if !hMod
        hMod := DllCall("LoadLibrary", "str", "gdiplus", "ptr")
    if !hMod
        throw Error("Failed to load gdiplus.dll")

    ; Initialize GDI+
    si := Buffer(16, 0)
    NumPut("UInt", 1, si)
    result := DllCall("gdiplus\GdiplusStartup", "ptr*", &pToken := 0, "ptr", si, "ptr", 0)
    if (result != 0)
        throw Error("GDI+ startup failed with code: " result)

    ; Create bitmap from HBITMAP
    result := DllCall("gdiplus\GdipCreateBitmapFromHBITMAP", "ptr", hBitmap, "ptr", 0, "ptr*", &pBitmap := 0)
    if (result != 0) {
        DllCall("gdiplus\GdiplusShutdown", "ptr", pToken)
        throw Error("Failed to create GDI+ bitmap from HBITMAP, code: " result)
    }

    ; Hardcode PNG CLSID
    CLSID := Buffer(16, 0)
    DllCall("ole32\CLSIDFromString", "wstr", "{557CF406-1A04-11D3-9A73-0000F81EF32E}", "ptr", CLSID)

    ; Save image to file
    result := DllCall("gdiplus\GdipSaveImageToFile", "ptr", pBitmap, "wstr", outFile, "ptr", CLSID, "ptr", 0)

    ; Cleanup
    DllCall("gdiplus\GdipDisposeImage", "ptr", pBitmap)
    DllCall("gdiplus\GdiplusShutdown", "ptr", pToken)

    ; Check save result after cleanup
    if (result != 0)
        throw Error("Failed to save image to file, GDI+ code: " result)
}

; --- Save bitmap with retry logic and validation ---
SaveBitmapWithRetry(hBitmap, outFile) {
    ; Ensure output directory exists
    outputDir := RegExReplace(outFile, "\\[^\\]*$", "")
    if (!DirExist(outputDir)) {
        try {
            DirCreate outputDir
        } catch Error as e {
            UpdatePreviewStatus("Failed to create output directory: " e.Message)
            return false
        }
    }

    maxRetries := 5
    baseDelay := 500  ; Start with 500ms delay

    Loop maxRetries {
        ; Update status in preview GUI
        UpdatePreviewStatus("Save attempt " A_Index "/" maxRetries "...")

        try {
            ; Attempt to save the bitmap
            SaveBitmapToPNG(hBitmap, outFile)

            ; Validate the saved file
            if (IsValidSavedFile(outFile)) {
                if (A_Index > 1) {
                    UpdatePreviewStatus("File saved successfully on attempt " A_Index)
                    Sleep 1000  ; Brief pause to show success message
                }
                return true  ; Success
            }

        } catch Error as e {
            ; Show detailed error in status
            UpdatePreviewStatus("Save attempt " A_Index " failed: " e.Message)

            ; Use enhanced error handler
            HandleFileSystemError("bitmap save attempt " A_Index, outFile, e.Message)

            ; Check if it's a critical error that already triggered restart
            if InStr(StrLower(e.Message), "access denied") || InStr(StrLower(e.Message), "sharing violation") {
                return false  ; Critical error, don't continue retrying
            }
        }

        ; If not the last attempt, wait before retrying
        if (A_Index < maxRetries) {
            ; Progressive delay: 500ms, 1000ms, 1500ms, 2000ms
            delay := baseDelay * A_Index
            UpdatePreviewStatus("Retrying in " (delay/1000) " seconds... (Attempt " (A_Index + 1) "/" maxRetries ")")
            Sleep delay
        }
    }

    return false  ; All retries failed
}

; --- Validate that file was saved correctly ---
IsValidSavedFile(filePath) {
    ; Check if file exists
    if (!FileExist(filePath))
        return false

    ; Check file size (should be > 1KB for a valid PNG)
    try {
        size := FileGetSize(filePath)
        if (size < 1024)
            return false
    } catch {
        return false
    }

    ; Try to read the file to ensure it's not locked/corrupted
    try {
        file := FileOpen(filePath, "r")
        if (!file)
            return false

        ; Read first few bytes to check PNG signature
        headerBuf := Buffer(8)
        file.RawRead(headerBuf)
        file.Close()

        ; PNG files start with specific signature: 89 50 4E 47 0D 0A 1A 0A
        ; Check at least the first 4 bytes match PNG signature
        return (NumGet(headerBuf, 0, "UChar") = 0x89 &&
                NumGet(headerBuf, 1, "UChar") = 0x50 &&
                NumGet(headerBuf, 2, "UChar") = 0x4E &&
                NumGet(headerBuf, 3, "UChar") = 0x47)

    } catch {
        return false
    }
}

; --- Upload file to 0x0.st using curl (with enhanced retries; no API key required) ---
UploadTo0x0(filePath) {
    if !FileExist(filePath)
        return ""

    maxRetries := config.image.upload_max_retries
    baseDelay := config.image.upload_base_delay
    lastError := ""  ; To store curl error for debugging
    tempDir := config.paths.temp_dir
    curlPath := config.paths.curl_path

    Loop maxRetries {
        tmpOut := tempDir "\0x0_result.txt"
        tmpErr := tempDir "\0x0_error.txt"
        try FileDelete tmpOut
        try FileDelete tmpErr

        ; Show progress in preview GUI
        if (config.ui.show_progress) {
            UpdatePreviewStatus("Uploading... Attempt " A_Index "/" maxRetries)
        }

        ; Run curl with --fail, redirect stdout and stderr
        cmd := curlPath ' -s --fail -F "file=@' filePath '" https://0x0.st > "' tmpOut '" 2> "' tmpErr '"'
        RunWait A_ComSpec ' /C ' cmd,, "Hide"

        ; Read result and error
        if FileExist(tmpOut) {
            out := Trim(FileRead(tmpOut))
        } else {
            out := ""
        }
        if FileExist(tmpErr) {
            lastError := Trim(FileRead(tmpErr))
        }
        try FileDelete tmpOut
        try FileDelete tmpErr

        ; Check if it's a valid 0x0.st URL (must contain "https://0x0.st/")
        if InStr(out, "https://0x0.st/") && (lastError = "") {
            if (config.ui.show_progress) {
                UpdatePreviewStatus("Upload completed successfully")
            }
            return out  ; Success
        }

        ; If not successful and not the last attempt, retry with exponential backoff
        if (A_Index < maxRetries) {
            delay := baseDelay * (2 ** (A_Index - 1))  ; Exponential backoff
            Sleep delay
        }
    }

    if (config.ui.show_progress) {
        UpdatePreviewStatus("Upload failed after all attempts")
    }
    MsgBox "Upload failed after " maxRetries " attempts. Last error: " (lastError ? lastError : "No output from server."), "Upload Error", 48
    return ""  ; All retries failed
}

; --- API call to get image description/response (supports URL or local file) ---
GetImageDescription(apiUrl, apiKey, modelName, imageSource, prompt) {
    maxRetries := config.api.max_retries
    baseDelay := config.api.base_delay
    timeout := config.api.timeout

    Loop maxRetries {
        ; Decide if remote URL or local file
        if (SubStr(imageSource, 1, 4) = "http") {
            imageJson := '{"type": "image_url", "image_url": {"url": "' imageSource '"}}'
        } else if FileExist(imageSource) {
            ; Detect MIME type from extension (simplified for PNG since we're saving as PNG)
            mime := "image/png"
            b64 := FileToBase64(imageSource)
            ; Basic validation to catch encoding errors
            if (b64 = "" || StrLen(b64) < 100 || !RegExMatch(b64, "^[A-Za-z0-9+/=]+$")) {
                return "Error: Invalid base64 encoding. Check the image file."
            }
            ; Ensure no CR/LF or whitespace sneaks in
            b64 := RegExReplace(b64, "[\r\n\t ]", "")
            imageJson := '{"type": "image_url", "image_url": {"url": "data:' mime ';base64,' b64 '"}}'
        } else {
            ; Assume already base64 string (fallback: png)
            ; Fallback: if not a file and not a URL, assume raw base64 string, sanitize
            b64 := RegExReplace(imageSource, "[\r\n\t ]", "")
            imageJson := '{"type": "image_url", "image_url": {"url": "data:image/png;base64,' b64 '"}}'
        }

        ; Build payload with proper escaping for prompt (to avoid JSON issues)
        escapedPrompt := StrReplace(prompt, '"', '\"')
        json := '{"model": "' modelName '", "messages": [{"role": "user", "content": ['
                . '{"type": "text", "text": "' escapedPrompt '"},'
                . imageJson . ']}]}'

        http := ComObject("MSXML2.XMLHTTP")  ; Using MSXML2 for better async/ReadyState support
        http.Open("POST", apiUrl, true)  ; Async mode
        http.SetRequestHeader("Content-Type", "application/json")
        http.SetRequestHeader("Authorization", "Bearer " apiKey)
        ; Do NOT set Content-Length manually; MSXML2 will set correct byte length.
        http.Send(json)

        ; Basic progress indicator in preview GUI if enabled
        if (config.ui.show_progress) {
            dots := 0
            startTime := A_TickCount
            UpdatePreviewStatus("Fetching AI response")
            while (http.ReadyState != 4) {
                if (A_TickCount - startTime > timeout) {
                    UpdatePreviewStatus("Request timed out")
                    return "Error: Request timed out."
                }
                ; Animate dots (cycle 0-3)
                dots := Mod(dots + 1, 4)
                UpdatePreviewStatus("Fetching AI response" StrRepeat(".", dots))
                Sleep 300  ; Update every 300ms
            }
            UpdatePreviewStatus("Processing response...")
        } else {
            ; Wait without progress indicator
            startTime := A_TickCount
            while (http.ReadyState != 4) {
                if (A_TickCount - startTime > timeout) {
                    return "Error: Request timed out."
                }
                Sleep 100  ; Check every 100ms
            }
        }

        ; Check status (handle 1223 as success due to MSXML2 bug)
        if (http.Status = 200 || http.Status = 1223) {
            respText := http.ResponseText

            ; Extract assistant message via regex (improved to handle multi-line content)
            m := RegExMatch(respText, 's)"content":\s*"(.*?)"(?=\s*,|\s*\})', &match)  ; 's' for dotall, non-greedy match
            if (match && match.Count) {
                return StrReplace(match[1], "\n", "`n")
            } else {
                return "Error: could not parse.`nRaw:`n" respText
            }
        } else if (http.Status = 400 && A_Index < maxRetries) {
            ; Retry logic for 400 with backoff
            UpdatePreviewStatus("HTTP 400 - Retrying (Attempt " (A_Index + 1) "/" maxRetries ")")
            delay := baseDelay * (2 ** (A_Index - 1))  ; Exponential backoff
            Sleep delay
        } else {
            return "Error: HTTP " http.Status "`n" http.StatusText "`nResponse Body: " http.ResponseText
        }
    }
    return "Error: Failed after " maxRetries " retries. Try again later."
}

; --- File to Base64 (reliable version using crypt32.dll; avoids ADODB/MSXML errors) ---
FileToBase64(filePath) {
    file := FileOpen(filePath, "r")
    if !IsObject(file)
        return ""  ; File not found or can't open

    size := file.Length
    bin := Buffer(size)
    file.RawRead(bin, size)  ; Read raw binary
    file.Close()

    ; Use CryptBinaryToString for base64 encoding (CRYPT_STRING_BASE64 | CRYPT_STRING_NOCRLF)
    reqSize := 0
    DllCall("crypt32\CryptBinaryToString", "Ptr", bin, "UInt", size, "UInt", 0x40000001, "Ptr", 0, "UIntP", &reqSize)
    if (!reqSize)
        return ""  ; Failed to calculate size

    str := Buffer(reqSize * 2)  ; UTF-16 buffer
    if !DllCall("crypt32\CryptBinaryToString", "Ptr", bin, "UInt", size, "UInt", 0x40000001, "Ptr", str, "UIntP", &reqSize)
        return ""  ; Conversion failed

    return StrGet(str, "UTF-16")
}

; --- Utility: Repeat a string n times ---
StrRepeat(str, count) {
    result := ""
    Loop count
        result .= str
    return result
}

; --- Post-process math notation to plain text (fallback) ---
PostProcessMath(text) {
    ; Replace frac{a}{b} with a/b
    text := RegExReplace(text, "i)frac\{(\w+)\}\{(\w+)\}", "$1/$2")
    ; Replace ^2 with ² (Unicode superscript)
    text := StrReplace(text, "^2", "²")
    ; Replace ^3 with ³
    text := StrReplace(text, "^3", "³")
    ; Add more replacements as needed (e.g., for other superscripts)
    text := StrReplace(text, "^4", "⁴")
    text := StrReplace(text, "^5", "⁵")
    ; Normalize line breaks
    text := RegExReplace(text, "m)\n\s*", "`n")
    return Trim(text)
}




; --- Check basic image validity (exists, > 1KB, readable) ---
IsValidImage(path) {
    if !FileExist(path)
        return false
    try size := FileGetSize(path)
    catch
        return false
    return (size && size > 1024)
}

; --- Extract simplified answer "(X) text" from model output ---
ExtractSimpleAnswer(text) {
    if (text = "")
        return "(?) (no answer)"
    if InStr(text, "Error:")
        return text
    ansText := ""
    choice := ""
    if RegExMatch(text, "i)(?:Result/Answer|Answer):\s*(.+?)(?:\R|$)", &m)
        ansText := Trim(m[1])
    if RegExMatch(text, "i)(?:Result/choice|Choice):\s*([A-D])", &m)
        choice := m[1]
    ; If options section exists and matches choice, prefer option text
    if (choice != "") {
        if RegExMatch(text, "is)" choice "\)\s*(.+?)(?=\s*[A-D]\)|$)", &mm)
            ansText := Trim(mm[1])
    }
    shown := ansText != "" ? ansText : "(no answer)"
    return (choice != "" ? "(" choice ") " shown : shown)
}

; --- Minimal preview GUI (top image, bottom answer text) ---
ShowSimplePreview(imagePath, answerText, deleteOnClose := "") {
    try {
        global previewGui
        ; Destroy previous preview window if it exists
        try previewGui.Destroy()
        previewGui := Gui("+AlwaysOnTop -MinSize300x200", "Snip Preview")
        previewGui.MarginX := 10, previewGui.MarginY := 10
        previewGui.SetFont("s12", "Segoe UI")

        ; Store delete path on the GUI so handlers can access/modify it
        previewGui.DeletePath := deleteOnClose

        previewGui.Add("Picture", "w600 h-1 vPic", imagePath)
        previewGui.Add("Text", "y+10", "Answer:")
        edt := previewGui.Add("Edit", "w600 r3 ReadOnly vAns", answerText)

        ; Progress area (hidden until started)
        progText := previewGui.Add("Text", "y+6 w600 h40 vProgText", "")
        prog := previewGui.Add("Progress", "w600 h12 vProg 0x08", 0)  ; 0x08 = PBS_MARQUEE (modern snake style)
        DllCall("SendMessage", "Ptr", prog.Hwnd, "UInt", 0x40A, "Ptr", 1, "Ptr", 50) ; PBM_SETMARQUEE
        prog.Visible := false
        progText.Visible := false

        ; Buttons row
        previewGui.Add("Button", "y+10 w90 h28 vBtnRecap", "Recapture")
        previewGui.Add("Button", "x+10 w90 h28 vBtnRetry", "Retry Send")
        previewGui.Add("Button", "x+10 w90 h28 vBtnClose", "Close")

        ; Handlers
        previewGui["BtnClose"].OnEvent("Click", (*) => HideToTray())
        previewGui["BtnRecap"].OnEvent("Click", (*) => RecaptureFromPreview())
        previewGui["BtnRetry"].OnEvent("Click", (*) => RetrySendCurrentImage())
        ; Also delete on window close via [X]
        previewGui.OnEvent("Close", (*) => ClosePreviewHandler())

        previewGui.Show()
    } catch Error as e {
        ; As a fallback, show text-only box
        MsgBox answerText
    }
}
; Hide application to system tray
HideToTray(*) {
    global previewGui
    try {
        if IsSet(previewGui) && previewGui.DeletePath {
            TryDeleteFile(previewGui.DeletePath)
        }
        if IsSet(previewGui) {
            previewGui.Hide()
        }
    }
    ; Hide the main script window to tray
    WinHide A_ScriptHwnd
}

; Close handler for preview GUI (used by window [X])
ClosePreviewHandler(*) {
    HideToTray()
}

; Recapture handler from preview: restart app for fresh capture
RecaptureFromPreview(*) {
    ; Clean up current captured file and preview GUI before restart
    global previewGui
    try {
        if IsSet(previewGui) && previewGui.DeletePath {
            TryDeleteFile(previewGui.DeletePath)
        }
        if IsSet(previewGui) {
            try previewGui.Destroy()
        }
    }

    ; Also clean up any existing Snip files in output directory
    try {
        Loop Files, config.paths.output_dir "\Snip_*.png" {
            TryDeleteFile(A_LoopFileFullPath)
        }
    }

    ; Clean restart for fresh GDI+ state
    Reload
}

; Retry sending the current image shown in preview to the AI
RetrySendCurrentImage(*) {
    try {
        global previewGui
        if !IsSet(previewGui)
            return
        imagePath := previewGui.DeletePath
        ; If DeletePath empty, try Pic control path; else we can't safely know path
        if (!imagePath) {
            try imagePath := previewGui["Pic"].Value
        }
        if (!imagePath || !FileExist(imagePath)) {
            MsgBox "No image available to retry."
            return
        }
        PreviewStartProgress("Retrying...")
        useBase64 := UseBase64Encoding()
        if useBase64 {
            imageSource := imagePath
        } else {
            uploadedUrl := UploadTo0x0(imagePath)
            imageSource := (uploadedUrl != "") ? uploadedUrl : imagePath
        }
        answer := GetImageDescription(config.api.url, config.api.key, config.api.model, imageSource, config.prompt.default)
        answer := PostProcessMath(answer)
        simple := ExtractSimpleAnswer(answer)
        PreviewSetAnswer(simple)
        PreviewStopProgress()
    } catch Error as e {
        PreviewStopProgress()
        MsgBox "Retry error: " e.Message
    }
}

; Start, update, and stop progress helpers for the preview GUI
PreviewStartProgress(text := "Processing...") {
    global previewGui
    if !IsSet(previewGui)
        return
    try {
        previewGui["Prog"].Visible := true
        previewGui["ProgText"].Visible := true
        UpdatePreviewStatus(text)
        previewGui["Prog"].Value := 0
        SetTimer AnimatePreviewProgress, 100
    }
}

PreviewSetAnswer(answer) {
    global previewGui
    try previewGui["Ans"].Value := answer
}

; Update status text in preview GUI
UpdatePreviewStatus(statusText) {
    global previewGui
    try {
        if IsSet(previewGui) {
            previewGui["ProgText"].Text := statusText
            previewGui["ProgText"].Visible := true  ; Ensure it's visible
        }
    } catch {
        ; Ignore if preview GUI doesn't exist or control not found
    }
}

PreviewStopProgress() {
    global previewGui
    try {
        SetTimer AnimatePreviewProgress, 0
        previewGui["Prog"].Visible := false
        previewGui["ProgText"].Visible := false
    }
}

AnimatePreviewProgress(*) {
    global previewGui
    static dir := 1
    try {
        val := previewGui["Prog"].Value + (10 * dir)
        if (val >= 100) {
            val := 100, dir := -1
        } else if (val <= 0) {
            val := 0, dir := 1
        }
        previewGui["Prog"].Value := val
    }
}





; Safe file delete helper with enhanced retry logic
TryDeleteFile(path) {
    if (!path || !FileExist(path))
        return true

    ; Try multiple times with increasing delays for locked files
    Loop 5 {
        try {
            FileSetAttrib "-R", path
            FileDelete path
            return true  ; Success
        } catch {
            if (A_Index < 5) {
                Sleep 300 * A_Index  ; 300ms, 600ms, 900ms, 1200ms, 1500ms
            }
        }
    }

    ; Final attempt with system command if still locked
    try {
        RunWait 'cmd /c del /f /q "' path '"',, "Hide"
        ; Check if deletion was successful
        Sleep 100
        return !FileExist(path)
    } catch {
        ; File is persistently locked
        return false
    }
}

; --- Enhanced error handler for file system issues ---
HandleFileSystemError(operation, filePath, errorMsg) {
    ; Update status in preview GUI instead of tooltip
    UpdatePreviewStatus("Error during " operation ": " errorMsg)

    ; Check if it's a critical error that requires restart
    criticalErrors := [
        "access denied",
        "sharing violation",
        "file is being used",
        "disk full",
        "path not found",
        "invalid handle",
        "corrupted"
    ]

    errorLower := StrLower(errorMsg)
    for criticalError in criticalErrors {
        if InStr(errorLower, criticalError) {
            ; Show error in progress text instead of message box
            UpdatePreviewStatus("Critical Error: " errorMsg " - Restarting application...")
            Sleep 3000  ; Give user time to read the error

            ; Clean up the problematic file before restart
            if (filePath && FileExist(filePath)) {
                TryDeleteFile(filePath)
            }
            Reload
            return
        }
    }

    ; For non-critical errors, update status but continue
    UpdatePreviewStatus("Warning: " operation " - " errorMsg)
    Sleep 2000  ; Brief pause to show warning
}

; --- Verify file system health before operations ---
VerifyFileSystemHealth(dirPath) {
    try {
        ; Test write access to output directory
        testFile := dirPath "\test_" A_TickCount ".tmp"
        FileAppend "test", testFile

        ; Test read access
        content := FileRead(testFile)

        ; Test delete access
        FileDelete testFile

        return true

    } catch Error as e {
        ; Clean up test file if it exists
        try TryDeleteFile(testFile)
        HandleFileSystemError("file system health check", dirPath, e.Message)
        return false
    }
}



